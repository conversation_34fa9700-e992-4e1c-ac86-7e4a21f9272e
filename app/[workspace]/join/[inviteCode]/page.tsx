'use client';
import React, { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { Button, Typography, Space, Spin, Alert, Result } from 'antd';
import { UserAddOutlined, CheckCircleOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import Image from "next/image";
import logo from "@/app/images/logo.png";
import Hivechat from "@/app/images/hivechat.svg";
import { useSession } from 'next-auth/react';
import { validateInviteCode } from '../../admin/users/invite/actions';
import { joinWorkspaceByInvite } from './actions';

const { Title, Text } = Typography;

interface JoinWorkspacePageProps { }

const JoinWorkspacePage: React.FC<JoinWorkspacePageProps> = () => {
  const params = useParams();
  const router = useRouter();
  const { data: session, status } = useSession();

  const workspaceId = params.workspace as string;
  const inviteCode = params.inviteCode as string;

  const [loading, setLoading] = useState(true);
  const [joining, setJoining] = useState(false);
  const [inviteValid, setInviteValid] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  // 验证邀请码
  const validateInvite = async () => {
    try {
      setLoading(true);
      const result = await validateInviteCode(workspaceId, inviteCode);

      if (result.valid) {
        setInviteValid(true);
        setError(null);
      } else {
        setInviteValid(false);
        setError(result.message || '邀请码无效');
      }
    } catch (error) {
      console.error('Validate invite error:', error);
      setError('验证邀请码时发生错误');
    } finally {
      setLoading(false);
    }
  };

  // 加入工作空间
  const handleJoinWorkspace = async () => {
    if (!session?.user?.id) {
      // 如果用户未登录，跳转到登录页面
      router.push(`/login?callbackUrl=${encodeURIComponent(window.location.href)}`);
      return;
    }

    try {
      setJoining(true);
      const result = await joinWorkspaceByInvite(workspaceId, inviteCode);

      if (result.status === 'success') {
        setSuccess(true);
        // 3秒后跳转到工作空间
        setTimeout(() => {
          router.push(`/${workspaceId}/chat`);
        }, 3000);
      } else {
        setError(result.message || '加入工作空间失败');
      }
    } catch (error) {
      console.error('Join workspace error:', error);
      setError('加入工作空间时发生错误');
    } finally {
      setJoining(false);
    }
  };

  useEffect(() => {
    if (workspaceId && inviteCode) {
      validateInvite();
    }
  }, [workspaceId, inviteCode]);

  // 如果用户认证状态还在加载中
  if (status === 'loading') {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: '100vh'
      }}>
        <Spin size="large" />
      </div>
    );
  }

  // 如果加入成功
  if (success) {
    return (
      <div className="min-h-screen login-page-bg flex">
        {/* 左侧内容区域 */}
        <div className="hidden lg:flex lg:flex-1 lg:flex-col lg:justify-center lg:px-20 xl:px-24">
          <div className="mx-auto max-w-xl -mt-20">
            {/* Logo */}
            <div className="flex items-center mb-8">
              <Image src={logo} alt="HiveChat logo" width={40} height={40} />
              <Hivechat className="ml-3" alt="HiveChat text" width={180} height={45} />
            </div>

            {/* 主标题 */}
            <h3 className="text-3xl font-bold leading-10 !text-gray-800 mb-6">
              为团队准备的一站式 AI 助手<br />灵活、便捷、免费
            </h3>

            {/* 副标题 */}
            <Text className="text-lg text-gray-600 leading-relaxed">
              支持顶尖人工智能模型、包括 OpenAI、Claude、Gemini、DeepSeek。
            </Text>
          </div>
        </div>

        {/* 右侧成功信息区域 */}
        <div className="flex flex-1 flex-col justify-center px-4 py-12 sm:px-6 lg:flex-none w-1/2 lg:px-24 xl:px-24">
          <div className="mx-auto w-full max-w-sm lg:w-96">
            {/* 移动端Logo */}
            <div className="flex items-center justify-center mb-8 lg:hidden">
              <Image src={logo} alt="HiveChat logo" width={32} height={32} />
              <Hivechat className="ml-2" alt="HiveChat text" width={156} height={39} />
            </div>

            {/* 成功卡片 */}
            <div className="bg-white py-8 px-6 login-form-shadow rounded-lg sm:px-10">
              <Result
                icon={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
                title="成功加入工作空间！"
                subTitle="正在跳转到聊天页面..."
                extra={
                  <Button
                    type="primary"
                    size="large"
                    className="!h-12 !text-base !font-medium"
                    onClick={() => router.push(`/${workspaceId}/chat`)}
                  >
                    立即前往
                  </Button>
                }
              />
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen login-page-bg flex">
      {/* 左侧内容区域 */}
      <div className="hidden lg:flex lg:flex-1 lg:flex-col lg:justify-center lg:px-20 xl:px-24">
        <div className="mx-auto max-w-xl -mt-20">
          {/* Logo */}
          <div className="flex items-center mb-8">
            <Image src={logo} alt="HiveChat logo" width={40} height={40} />
            <Hivechat className="ml-3" alt="HiveChat text" width={180} height={45} />
          </div>

          {/* 主标题 */}
          <h3 className="text-3xl font-bold leading-10 !text-gray-800 mb-6">
            为团队准备的一站式 AI 助手<br />灵活、便捷、免费
          </h3>

          {/* 副标题 */}
          <Text className="text-lg text-gray-600 leading-relaxed">
            支持顶尖人工智能模型、包括 OpenAI、Claude、Gemini、DeepSeek。
          </Text>
        </div>
      </div>

      {/* 右侧邀请卡片区域 */}
      <div className="flex flex-1 flex-col justify-center px-4 py-12 sm:px-6 lg:flex-none w-1/2 lg:px-24 xl:px-24">
        <div className="mx-auto w-full max-w-sm lg:w-96">
          {/* 移动端Logo */}
          <div className="flex items-center justify-center mb-8 lg:hidden">
            <Image src={logo} alt="HiveChat logo" width={32} height={32} />
            <Hivechat className="ml-2" alt="HiveChat text" width={156} height={39} />
          </div>

          {/* 邀请卡片 */}
          <div className="bg-white py-8 px-6 login-form-shadow rounded-lg sm:px-10">
            {loading ? (
              <div className="text-center">
                <Spin size="large" />
                <div style={{ marginTop: 16 }}>
                  <Text>验证邀请码中...</Text>
                </div>
              </div>
            ) : error ? (
              <Result
                icon={<ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />}
                title="邀请无效"
                subTitle={error}
                extra={
                  <Button type="primary" onClick={() => router.push('/')}>
                    返回首页
                  </Button>
                }
              />
            ) : inviteValid ? (
              <Space direction="vertical" size="large" style={{ width: '100%' }}>
                <div className="text-center">
                  <UserAddOutlined style={{ fontSize: 48, color: '#1890ff', marginBottom: 16 }} />
                  <Title level={3} className="!text-2xl !font-semibold !text-gray-900 !mb-2">
                    加入工作空间
                  </Title>
                  <Text type="secondary" className="text-base">
                    您收到了加入工作空间的邀请
                  </Text>
                </div>

                {!session ? (
                  <div className="space-y-4">
                    <Alert
                      message="需要登录"
                      description="请先登录您的账户以加入工作空间"
                      type="info"
                      showIcon
                    />
                    <Button
                      type="primary"
                      size="large"
                      block
                      className="!h-12 !text-base !font-medium"
                      onClick={() => router.push(`/login?callbackUrl=${encodeURIComponent(window.location.href)}`)}
                    >
                      登录账户
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <div className="text-center">
                      <Text className="text-base">
                        您将以 <strong>{session.user.email}</strong> 的身份加入工作空间
                      </Text>
                    </div>
                    <Button
                      type="primary"
                      size="large"
                      block
                      loading={joining}
                      className="!h-12 !text-base !font-medium"
                      onClick={handleJoinWorkspace}
                    >
                      {joining ? '加入中...' : '确认加入'}
                    </Button>
                  </div>
                )}
              </Space>
            ) : null}
          </div>
        </div>
      </div>
    </div>
  );
};

export default JoinWorkspacePage;
